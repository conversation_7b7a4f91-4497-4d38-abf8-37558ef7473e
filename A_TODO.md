# Valgrind Memory Issues TODO List

## Critical Memory Management Issues Found

### 1. Object Lifecycle Management

#### 1.1 Corpse Objects Memory Leaks
- [ ] **CRITICAL**: In `fight.c:make_corpse()` line 1735, money object is created but immediately orphaned
  - Money is added to room THEN to corpse container (double placement)
  - `obj_to_room(money, IN_ROOM(ch));` followed by `obj_to_obj(money, corpse);`
  - This creates a dangling reference as object can't be in two places
- [ ] Review corpse extraction to ensure `char_sdesc` field is always freed
- [ ] Verify corpse timer expiration properly extracts all contained objects

#### 1.2 Container/Contents Management
- [ ] Audit `extract_obj()` recursive extraction of contents (line 2347)
  - Ensure no circular references between containers
  - Verify parent-child relationships are properly severed
- [ ] Check `object_list_new_owner()` for proper ownership transfer
  - Function recursively updates carried_by but doesn't validate consistency

#### 1.3 Object String Management
- [ ] Review `free_object_strings()` vs `free_object_strings_proto()`
  - Prototype strings may be freed incorrectly
  - Ensure string ownership is tracked properly
- [ ] Audit all places where object strings are duplicated with `strdup()`
  - Many places don't check for existing strings before assignment

### 2. Character Memory Management

#### 2.1 Player Special Data
- [ ] Fix potential leak in `free_char()` for NPCs with player_specials
  - Line 5656 logs error but doesn't prevent the leak
- [ ] Audit account name string management
  - Multiple places duplicate account names without freeing old ones

#### 2.2 Action Queue Memory
- [ ] Verify `free_action_queue()` and `free_attack_queue()` are always called
  - Check all character extraction paths
  - Ensure queues are cleared on character death/logout

#### 2.3 Spell/Skill Data
- [ ] Review spell preparation queue cleanup
  - `destroy_spell_prep_queue()` may not free all allocated nodes
- [ ] Check innate magic queue cleanup
- [ ] Verify spell collection and known spells are properly freed

### 3. Script System Memory

#### 3.1 Script Variables
- [ ] Audit DG script variable cleanup
  - Script variables may persist after script extraction
  - Check for leaked variable lists

#### 3.2 Proto Scripts
- [ ] Review `free_proto_script()` usage
  - Ensure prototype scripts aren't freed while instances exist
  - Check reference counting for shared scripts

### 4. Event System Memory

#### 4.1 Event Cleanup
- [ ] Verify all events are cancelled before freeing event lists
  - `free_list()` doesn't cancel events, just frees the list structure
- [ ] Check for events that reference freed objects/characters
  - Events may hold stale pointers after target extraction

### 5. Database/MySQL Related

#### 5.1 Result Set Management
- [ ] Audit all MySQL query results for proper cleanup
  - Many queries don't call `mysql_free_result()`
  - Check for leaked result sets in error paths

#### 5.2 String Escaping
- [ ] Review `mysql_real_escape_string()` usage
  - Escaped strings are often allocated but not freed
  - Check for buffer overflows in escape operations

### 6. Specific Memory Leak Patterns

#### 6.1 Equipment/Inventory Transfer
- [ ] Fix equipment transfer in `make_corpse()`
  - Equipment is unequipped but references may persist
  - Check `obj->worn_by` cleanup

#### 6.2 Room/Zone Memory
- [ ] Audit room description strings
  - Dynamic room descriptions may leak on zone resets
  - Check trail data cleanup in wilderness areas

#### 6.3 Shop/Trade Memory
- [ ] Review shop inventory management
  - Shop objects may not be properly freed on shutdown
  - Check trade transaction temporary object cleanup

### 7. Circular Reference Issues

#### 7.1 Object Containment Loops
- [ ] Add circular reference detection in `obj_to_obj()`
  - Prevent objects from containing themselves
  - Detect loops in container hierarchies

#### 7.2 Character Following Chains
- [ ] Audit follower/master relationships
  - Check for circular following patterns
  - Ensure proper cleanup when breaking follow

### 8. Performance-Related Memory Issues

#### 8.1 String Duplication
- [ ] Replace excessive `strdup()` calls with reference counting
  - Many identical strings are duplicated unnecessarily
  - Implement string pooling for common strings

#### 8.2 Memory Fragmentation
- [ ] Implement object pooling for frequently allocated structures
  - Objects, characters, and events are constantly allocated/freed
  - Use fixed-size pools to reduce fragmentation

### 9. Testing and Validation

#### 9.1 Valgrind Test Suite
- [ ] Create automated valgrind tests for:
  - Object creation/destruction cycles
  - Character login/logout sequences
  - Combat and death scenarios
  - Container manipulation
  - Script execution

#### 9.2 Memory Tracking
- [ ] Add memory allocation tracking system
  - Track total allocations by type
  - Monitor for gradual memory growth
  - Log allocation/free mismatches

### 10. Code Quality Improvements

#### 10.1 Consistent Memory Management
- [ ] Establish clear ownership rules for all allocated memory
- [ ] Document memory management patterns in code
- [ ] Add assertions to verify memory invariants

#### 10.2 Safe String Handling
- [ ] Create safe string assignment functions
  - Automatically free existing strings
  - Handle NULL cases properly
  - Prevent double-frees

## Priority Order

1. **IMMEDIATE**: Fix corpse money object double-placement bug
2. **HIGH**: Audit and fix object extraction/cleanup paths
3. **HIGH**: Fix character memory leaks in free_char()
4. **MEDIUM**: Implement circular reference detection
5. **MEDIUM**: Add valgrind test automation
6. **LOW**: Optimize string management with pooling

## Notes

- Many issues stem from unclear ownership semantics
- Prototype vs instance management needs clear documentation
- Event system needs reference counting or weak references
- Consider implementing RAII patterns where possible in C